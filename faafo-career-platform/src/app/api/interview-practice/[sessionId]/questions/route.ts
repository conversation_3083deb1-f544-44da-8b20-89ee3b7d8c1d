import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { geminiService } from '@/lib/services/geminiService';
import { withErrorHandler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';

// Validation schema for generating questions
const generateQuestionsSchema = z.object({
  count: z.number().min(1).max(20).default(10),
  questionTypes: z.array(z.enum(['BEHAVIORAL', 'TECHNICAL', 'SITUATIONAL', 'COMPANY_CULTURE', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'STRESS_TEST', 'CASE_STUDY', 'ROLE_SPECIFIC'])).optional(),
  categories: z.array(z.enum(['GENERAL', 'TECHNICAL_SKILLS', 'SOFT_SKILLS', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'TEAMWORK', 'ADAPTABILITY', 'CREATIVITY', 'ANALYTICAL_THINKING', 'CUSTOMER_SERVICE', 'SALES', 'MANAGEMENT', 'STRATEGY', 'ETHICS', 'INDUSTRY_KNOWLEDGE'])).optional(),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
});

// GET - Retrieve questions for a session
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      try {
        // Verify session ownership
        const interviewSession = await prisma.interviewSession.findFirst({
          where: {
            id: sessionId,
            userId,
          },
        });

        if (!interviewSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        // Get questions with user responses
        const questions = await prisma.interviewQuestion.findMany({
          where: { sessionId },
          include: {
            responses: {
              where: { userId },
              select: {
                id: true,
                responseText: true,
                audioUrl: true,
                responseTime: true,
                preparationTime: true,
                aiScore: true,
                isCompleted: true,
                userNotes: true,
                createdAt: true,
              },
            },
          },
          orderBy: { questionOrder: 'asc' },
        });

        return NextResponse.json({
          success: true,
          data: questions,
        });
      } catch (error) {
        console.error('Error fetching interview questions:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch interview questions' },
          { status: 500 }
        );
      }
    }
  );
}

// POST - Generate questions for a session using AI
export const POST = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 generations per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      try {
        const body = await request.json();
        const validation = generateQuestionsSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const { count, questionTypes, categories, difficulty } = validation.data;

        // Verify session ownership and get session details
        const interviewSession = await prisma.interviewSession.findFirst({
          where: {
            id: sessionId,
            userId,
          },
        });

        if (!interviewSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        // Check if questions already exist
        const existingQuestions = await prisma.interviewQuestion.count({
          where: { sessionId },
        });

        if (existingQuestions > 0) {
          return NextResponse.json(
            { success: false, error: 'Questions already exist for this session' },
            { status: 400 }
          );
        }

        // Generate questions using AI
        const questionsResult = await geminiService.generateInterviewQuestions({
          sessionType: interviewSession.sessionType,
          careerPath: interviewSession.careerPath || undefined,
          experienceLevel: interviewSession.experienceLevel || undefined,
          companyType: interviewSession.companyType || undefined,
          industryFocus: interviewSession.industryFocus || undefined,
          specificRole: interviewSession.specificRole || undefined,
          interviewType: interviewSession.interviewType || undefined,
          focusAreas: interviewSession.focusAreas,
          difficulty: difficulty || interviewSession.difficulty,
          questionTypes,
          categories,
          count,
        });

        if (!questionsResult.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: questionsResult.error || 'Failed to generate questions' 
            },
            { status: 500 }
          );
        }

        // Save generated questions to database
        const questionsData = questionsResult.data.questions.map((q: any, index: number) => ({
          sessionId,
          questionText: q.questionText,
          questionType: q.questionType,
          category: q.category,
          difficulty: q.difficulty || difficulty || interviewSession.difficulty,
          expectedDuration: q.expectedDuration || 180,
          context: q.context,
          hints: q.hints,
          followUpQuestions: q.followUpQuestions,
          industrySpecific: q.industrySpecific || false,
          questionOrder: index + 1,
          isRequired: q.isRequired !== false,
          tags: q.tags,
        }));

        const createdQuestions = await prisma.interviewQuestion.createMany({
          data: questionsData,
        });

        // Update session with total questions count
        await prisma.interviewSession.update({
          where: { id: sessionId },
          data: {
            totalQuestions: questionsData.length,
            lastActiveAt: new Date(),
          },
        });

        // Fetch the created questions with full details
        const questions = await prisma.interviewQuestion.findMany({
          where: { sessionId },
          orderBy: { questionOrder: 'asc' },
        });

        return NextResponse.json({
          success: true,
          data: {
            questions,
            metadata: questionsResult.data.metadata,
          },
          message: `Generated ${questions.length} interview questions successfully`,
        });
      } catch (error) {
        console.error('Error generating interview questions:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to generate interview questions' },
          { status: 500 }
        );
      }
    }
  );
});
